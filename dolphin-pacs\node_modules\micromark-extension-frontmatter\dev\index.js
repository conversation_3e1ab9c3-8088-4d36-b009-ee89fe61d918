/**
 * @typedef {import('./lib/to-matters.js').Info} Info
 * @typedef {import('./lib/to-matters.js').Matter} Matter
 * @typedef {import('./lib/to-matters.js').Options} Options
 * @typedef {import('./lib/to-matters.js').Preset} Preset
 */

export {frontmatter} from './lib/syntax.js'
export {frontmatterHtml} from './lib/html.js'
export {toMatters} from './lib/to-matters.js'

// Note: we don’t have an `index.d.ts` in this extension because all token
// types are dynamic in JS
