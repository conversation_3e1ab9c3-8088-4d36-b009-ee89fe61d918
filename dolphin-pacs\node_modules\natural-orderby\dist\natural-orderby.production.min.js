/**
 * natural-orderby v5.0.0
 *
 * Copyright (c) <PERSON>
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
const e=(e,r)=>e<r?-1:e>r?1:0,r=(e,r,n)=>{const t=e.localeCompare(r,n);return t?t/Math.abs(t):0},n=/(^0x[\da-fA-F]+$|^([+-]?(?:\d+(?:\.\d*)?|\.\d+)(?:[eE][+-]?\d+)?(?!\.\d+)(?=\D|\s|$))|\d+)/g,t=/^\s+|\s+$/g,i=/\s+/g,o=/^[+-]?(?:\d+(?:\.\d*)?|\.\d+)(?:[eE][+-]?\d+)?$/,s=/(^([\w ]+,?[\w ]+)?[\w ]+,?[\w ]+\d+:\d+(:\d+)?[\w ]?|^\d{1,4}[/-]\d{1,4}[/-]\d{1,4}|^\w+, \w+ \d+, \d{4})/,a=/^0+[1-9]{1}[0-9]*$/,u=/[^\x00-\x80]/,c=(e,r)=>e<r?-1:e>r?1:0,l=(n,t,i)=>n.value===t.value?0:void 0!==n.parsedNumber&&void 0!==t.parsedNumber?e(n.parsedNumber,t.parsedNumber):n.chunks&&t.chunks?((n,t,i)=>{const o=n.length,s=t.length,a=Math.min(o,s);for(let l=0;l<a;l++){const o=n[l],s=t[l];if(o.normalizedString!==s.normalizedString){if(""===o.normalizedString!=(""===s.normalizedString))return""===o.normalizedString?-1:1;if(void 0!==o.parsedNumber&&void 0!==s.parsedNumber){const r=e(o.parsedNumber,s.parsedNumber);return 0===r?c(o.normalizedString,s.normalizedString):r}return void 0!==o.parsedNumber||void 0!==s.parsedNumber?void 0!==o.parsedNumber?-1:1:u.test(o.normalizedString+s.normalizedString)?r(o.normalizedString,s.normalizedString,i):c(o.normalizedString,s.normalizedString)}}return o>a||s>a?o<=a?-1:1:0})(n.chunks,t.chunks,i):((e,r)=>(e.chunks?!r.chunks:r.chunks)?e.chunks?-1:1:(e.isNaN?!r.isNaN:r.isNaN)?e.isNaN?-1:1:(e.isSymbol?!r.isSymbol:r.isSymbol)?e.isSymbol?-1:1:(e.isObject?!r.isObject:r.isObject)?e.isObject?-1:1:(e.isArray?!r.isArray:r.isArray)?e.isArray?-1:1:(e.isFunction?!r.isFunction:r.isFunction)?e.isFunction?-1:1:(e.isNull?!r.isNull:r.isNull)?e.isNull?-1:1:0)(n,t),d=e=>e.replace(i," ").replace(t,""),f=e=>{if(0!==e.length){const r=Number(e.replace(/_/g,""));if(!Number.isNaN(r))return r}},m=(e,r,n)=>{if(o.test(e)&&(!a.test(e)||0===r||"."!==n[r-1]))return f(e)||0},p=(e,r,n)=>({parsedNumber:m(e,r,n),normalizedString:d(e)}),N=e=>{const r=(e=>e.replace(n,"\0$1\0").replace(/\0$/,"").replace(/^\0/,"").split("\0"))(e).map(p);return r},b=e=>"function"==typeof e,y=e=>Number.isNaN(e)||e instanceof Number&&Number.isNaN(e.valueOf()),g=e=>null===e,S=e=>!(null===e||"object"!=typeof e||Array.isArray(e)||e instanceof Number||e instanceof String||e instanceof Boolean||e instanceof Date),v=e=>"symbol"==typeof e,h=e=>void 0===e,A=e=>{const r=f(e);return void 0!==r?r:(e=>{try{const r=Date.parse(e);return!Number.isNaN(r)&&s.test(e)?r:void 0}catch{return}})(e)},z=e=>{if("string"==typeof e||e instanceof String||("number"==typeof e||e instanceof Number)&&!y(e)||"boolean"==typeof e||e instanceof Boolean||e instanceof Date){const r=(e=>"boolean"==typeof e||e instanceof Boolean?Number(e).toString():"number"==typeof e||e instanceof Number?e.toString():e instanceof Date?e.getTime().toString():"string"==typeof e||e instanceof String?e.toLowerCase().replace(t,""):"")(e),n=A(r);return{parsedNumber:n,chunks:N(n?`${n}`:r),value:e}}return{isArray:Array.isArray(e),isFunction:b(e),isNaN:y(e),isNull:g(e),isObject:S(e),isSymbol:v(e),isUndefined:h(e),value:e}},j=e=>"string"==typeof e&&("asc"===e||"desc"===e);function k(e){return(e=>(r,n)=>{const t=z(r),i=z(n);return l(t,i,e.locale)*("desc"===e.order?-1:1)})((e=>{let r,n="asc";return"string"==typeof e&&j(e)?n=e:e&&"object"==typeof e&&(e.order&&j(e.order)&&(n=e.order),e.locale&&e.locale.length>0&&(r=e.locale)),{order:n,locale:r}})(e))}const w=e=>"function"==typeof e?e:r=>{if(Array.isArray(r)){const n=Number(e);if(Number.isInteger(n))return r[n]}else if(r&&"object"==typeof r){const n=Object.getOwnPropertyDescriptor(r,e);return n?.value}return r},x=(e,r,n,t)=>{const i=r.length?r.map(w):[e=>e],o=e.map(((e,r)=>({index:r,values:i.map((r=>r(e))).map(z)})));return o.sort(((e,r)=>((e,r,n,t)=>{const{index:i,values:o}=e,{index:s,values:a}=r,{length:u}=o,c=n.length;for(let d=0;d<u;d++){const e=d<c?n[d]:null;if(e&&"function"==typeof e){const r=e(o[d].value,a[d].value);if(r)return r}else{const r=l(o[d],a[d],t);if(r)return r*("desc"===e?-1:1)}}return i-s})(e,r,n,t))),o.map((r=>((e,r)=>e[r])(e,r.index)))};function O(e,r,n,t){if(!e||!Array.isArray(e))return[];const i=(e=>{if(!e)return[];const r=Array.isArray(e)?[...e]:[e];return r.some((e=>"string"!=typeof e&&"number"!=typeof e&&"function"!=typeof e))?[]:r})(r),o=(e=>{if(!e)return[];const r=Array.isArray(e)?[...e]:[e];return r.some((e=>"asc"!==e&&"desc"!==e&&"function"!=typeof e))?[]:r})(n);return x(e,i,o,t)}export{k as compare,O as orderBy};
